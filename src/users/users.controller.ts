import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { CurrentUser, CurrentUserData } from '@/common/decorators/current-user.decorator';
import { UsersService } from './users.service';
import { User } from './dto/user.dto';
import { UpdateUserRequest } from './dto/update-user-request.dto';
import { TwoFactorSetupResponse } from './dto/two-factor-setup-response.dto';
import { TwoFactorVerifyRequest } from './dto/two-factor-verify-request.dto';
import { TwoFactorEnabledResponse } from './dto/two-factor-enabled-response.dto';
import { TwoFactorDisableRequest } from './dto/two-factor-disable-request.dto';
import { BackupCodesResponse } from './dto/backup-codes-response.dto';
import { WebSession } from './dto/web-session.dto';
import { DesktopSession } from './dto/desktop-session.dto';
import { BotSession } from './dto/bot-session.dto';
import { SessionLimits } from './dto/session-limits.dto';
import { DesktopRegistrationResponse } from './dto/desktop-registration-response.dto';
import { Pagination } from './dto/pagination.dto';

@ApiTags('User Profile')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('/me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Success' })
  async getCurrentUser() {
    return this.usersService.getCurrentUser();
  }

  @Put('/me')
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ status: 200, description: 'Success' })
  async updateCurrentUser(@Body() body: any) {
    return this.usersService.updateCurrentUser(body);
  }
}
