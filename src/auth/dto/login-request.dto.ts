import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class LoginRequest {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'SecurePassword123!'
  })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({
    description: 'Two-factor authentication code (if enabled)',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString()
  twoFactorCode?: string;
}
