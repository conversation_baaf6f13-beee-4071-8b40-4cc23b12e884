# Simple Dockerfile for local development
FROM node:22-alpine

# Install system dependencies including build tools for native modules
RUN apk add --no-cache \
    curl \
    python3 \
    make \
    g++ \
    linux-headers

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies and rebuild native modules
RUN npm install && npm rebuild

# Copy source code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Expose port and debug port
EXPOSE 3000 9229

# Default command for development
CMD ["npm", "run", "start:dev"]
