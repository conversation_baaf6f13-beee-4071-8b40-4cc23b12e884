# Development Dockerfile for RSGlider API
FROM node:22-alpine

# Install system dependencies including build tools for native modules
RUN apk add --no-cache \
    curl \
    python3 \
    make \
    g++ \
    linux-headers

# Create app directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies (this will be cached if package.json doesn't change)
RUN npm ci --only=production=false

# Copy source code (excluding node_modules via .dockerignore)
COPY . .

# Rebuild native modules to ensure compatibility with container architecture
RUN npm rebuild

# Create uploads directory
RUN mkdir -p uploads

# Expose port and debug port
EXPOSE 3000 9229

# Default command for development
CMD ["npm", "run", "start:dev"]
